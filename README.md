# Домашнее задание к занятию «Отказоустойчивость в облаке»

## Задание 1

Возьмите за основу решение к заданию 1 из занятия «Подъём инфраструктуры в Яндекс Облаке».

Теперь вместо одной виртуальной машины необходимо создать Terraform Playbook, который выполняет следующие действия:

*   Создает 2 идентичные виртуальные машины. Используйте аргумент `count` для создания таких ресурсов.
*   Создает таргет-группу и помещает в неё созданные на предыдущем шаге виртуальные машины.
*   Создает сетевой балансировщик нагрузки, который:
    *   Слушает на порту 80.
    *   Отправляет трафик на порт 80 виртуальных машин.
    *   Выполняет HTTP healthcheck на порт 80 виртуальных машин.

Рекомендуем изучить документацию сетевого балансировщика нагрузки для более глубокого понимания.

### Настройка и проверка

1.  Установите на созданные виртуальные машины пакет Nginx любым удобным способом.
2.  Запустите Nginx веб-сервер на порту 80.
3.  Перейдите в веб-консоль Yandex Cloud и убедитесь, что:
    *   Созданный балансировщик находится в статусе `Active`.
    *   Обе виртуальные машины в целевой группе находятся в состоянии `healthy`.
4.  Сделайте запрос на порт 80 на внешний IP-адрес балансировщика и убедитесь, что вы получаете ответ в виде дефолтной страницы Nginx.

### Результаты

В качестве результата необходимо предоставить:

1.  **Terraform Playbook:**
    ```
    ./main.tf
    ```
2.  **Скриншот статуса балансировщика и целевой группы:**
    ```
    ./screen/1.png
    ```
3.  **Скриншот страницы, которая открылась при запросе IP-адреса балансировщика:**
    ```
    ./screen/2.png
    ```
    ```
    ./screen/3.png
    ```
